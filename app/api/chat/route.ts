import { openai } from "@ai-sdk/openai"
import { streamText } from "ai"

export const runtime = "edge"

export async function POST(req: Request) {
  const { messages } = await req.json()

  const result = await streamText({
    model: openai("gpt-3.5-turbo"),
    messages: messages.map((message: any) => ({
      content: message.content,
      role: message.role,
    })),
  })

  return result.toDataStreamResponse()
}

